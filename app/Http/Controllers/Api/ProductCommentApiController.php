<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Resources\Api\CommentResource;
use App\Models\ProductComment;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;

class ProductCommentApiController extends Controller
{
    use AuthorizesRequests;

    public function index(Request $request)
    {
        $query = ProductComment::query();
        if ($request->has('product_id')) {
            $query->where('product_id', $request->product_id);
        }
        $comments = $query->with([
            'user', 'product'])->latest()->paginate($request->get('per_page', 15)); 
        return response()->json([
            'success' => true,
            'data' => $comments
        ]);
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'id' => 'required|exists:products,id',
            'comment' => 'required|string',
        ]);
        $comment = ProductComment::create([
            'user_id' => Auth::id(),
            'product_id' => $validated['id'],
            'text' => $validated['comment'],
            'commented_at' => now(),
        ]);
        return response()->json([
            'success' => true,
            'data' => CommentResource::make($comment)
        ], 201);
    }

    public function show(ProductComment $productComment)
    {
        return response()->json([
            'success' => true,
            'data' => $productComment->load(['user', 'product'])
        ]);
    }

    public function update(Request $request, ProductComment $productComment)
    {
        $this->authorize('update', $productComment);
        $validated = $request->validate([
            'text' => 'required|string',
        ]);
        $productComment->update($validated);
        return response()->json([
            'success' => true,
            'data' => $productComment->load(['user', 'product'])
        ]);
    }

    public function destroy(ProductComment $productComment)
    {
        $this->authorize('delete', $productComment);
        $productComment->delete();
        return response()->json([
            'success' => true,
            'message' => 'Comment deleted'
        ]);
    }
} 