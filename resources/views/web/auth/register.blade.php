@extends('web.layouts.layout')
@section('title', __('إنشاء حساب جديد'))

@section('page-meta')
<meta name="description" content="{{ __('إنشاء حساب جديد في موقع حراجي للبيع والشراء') }}">
<meta name="keywords" content="{{ __('حراجي, تسجيل, إنشاء حساب, حساب جديد') }}">
@endsection

@section('content')
<main class="py-12">
    <div class="container mx-auto px-4">
        <div class="max-w-md mx-auto bg-base-100 rounded-lg shadow-md p-8">
            <h1 class="text-2xl font-bold text-center mb-6">{{ __('إنشاء حساب جديد') }}</h1>
            
            @if($errors->any())
                <div class="alert alert-error mb-6">
                    <ul class="list-disc list-inside">
                        @foreach($errors->all() as $error)
                            <li>{{ $error }}</li>
                        @endforeach
                    </ul>
                </div>
            @endif
            
            <form action="{{ route('web.auth.register') }}" method="POST">
                @csrf
                
                <div class="form-control mb-4">
                    <label class="label" for="name" >
                        <span class="label-text">{{ __('الاسم') }}</span>
                    </label>
                    <input type="text" name="name" value="{{ old('name') }}" class="input input-bordered w-full" required autofocus />
                </div>
                
                <div class="form-control mb-4">
                    <label class="label">
                        <span class="label-text">{{ __('البريد الإلكتروني') }}</span>
                    </label>
                    <input type="email" name="email" value="{{ old('email') }}" class="input input-bordered w-full" required />
                </div>
                
                <div class="form-control mb-4">
                    <label class="label">
                        <span class="label-text">{{ __('كلمة المرور') }}</span>
                    </label>
                    <input type="password" name="password" class="input input-bordered w-full" required />
                </div>
                
                <div class="form-control mb-6">
                    <label class="label">
                        <span class="label-text">{{ __('تأكيد كلمة المرور') }}</span>
                    </label>
                    <input type="password" name="password_confirmation" class="input input-bordered w-full" required />
                </div>
                
                <div class="form-control mb-6">
                    <label class="flex items-center gap-2 cursor-pointer">
                        <input type="checkbox" name="terms" class="checkbox checkbox-primary" required />
                        <span>
                            {{ __('أوافق على') }} 
                            <a href="route" class="text-primary hover:underline">{{ __('الشروط والأحكام') }}</a>
                        </span>
                    </label>
                </div>
                
                <div class="form-control">
                    <button type="submit" class="btn btn-primary">{{ __('إنشاء حساب') }}</button>
                </div>
            </form>
            
            <div class="divider my-6">{{ __('أو') }}</div>
            
            <p class="text-center">
                {{ __('لديك حساب بالفعل؟') }} 
                <a href="{{ route('web.auth.login') }}" class="text-primary hover:underline">{{ __('تسجيل الدخول') }}</a>
            </p>
        </div>
    </div>
</main>
@endsection
